"""
Flask API implementation for Connecto AI Voice Receptionist platform.

This module provides the API endpoints for the Connecto platform, including:
- User management
- Business profile management
- Agent configuration
- Call logs and statistics
- Voice preview functionality
"""

import os
import uuid
import json
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from functools import wraps

from flask import Flask, request, jsonify, send_from_directory, abort, g
from flask_cors import CORS
from werkzeug.utils import secure_filename

# Import database driver
from db_driver import (
    get_driver,
    register_business_for_connecto,
    get_business_stats,
    configure_agent_from_db,
    log_agent_call,
    APP_CONNECTO,
    APP_RESTAURANT_MGMT
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("connecto-api")

# Initialize Flask app
app = Flask(__name__)
app.config['JSON_SORT_KEYS'] = False
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size

# Configure CORS
CORS(app, resources={r"/*": {"origins": "*"}})

# Create a directory for static files if it doesn't exist
os.makedirs("static", exist_ok=True)
os.makedirs("static/audio", exist_ok=True)

# Standard response format
def create_response(data=None, message="Success", success=True):
    """
    Create a standardized API response.

    Args:
        data: The response data
        message: A message describing the response
        success: Whether the request was successful

    Returns:
        Dict containing the standardized response
    """
    return {
        "success": success,
        "message": message,
        "data": data,
    }

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')

        if not auth_header:
            return jsonify(create_response(
                success=False,
                message="Authentication token is missing",
                data=None
            )), 401

        try:
            # Extract token from Authorization header
            scheme, token = auth_header.split()
            if scheme.lower() != "bearer":
                return jsonify(create_response(
                    success=False,
                    message="Invalid authentication scheme",
                    data=None
                )), 401

            # Get Supabase driver
            driver = get_driver()

            # Verify token with Supabase
            user = driver.client.auth.get_user(token)

            if not user:
                return jsonify(create_response(
                    success=False,
                    message="Invalid token",
                    data=None
                )), 401

            # Store user in Flask's g object for the current request
            g.user = user

            return f(*args, **kwargs)

        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return jsonify(create_response(
                success=False,
                message="Invalid authentication credentials",
                data=None
            )), 401

    return decorated

# Error handler for API exceptions
@app.errorhandler(Exception)
def handle_exception(e):
    """
    Handle exceptions and return a consistent error response.
    """
    logger.error(f"API error: {str(e)}")

    if hasattr(e, 'code') and e.code:
        status_code = e.code
    else:
        status_code = 500

    return jsonify(create_response(
        success=False,
        message=str(e),
        data=None
    )), status_code

# API Endpoints

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint to verify the API is running.
    """
    return jsonify(create_response(data={"status": "healthy"}))

# Static files endpoint
@app.route('/static/<path:filename>')
def serve_static(filename):
    """
    Serve static files.
    """
    return send_from_directory('static', filename)

# User Management Endpoints

@app.route('/users/register', methods=['POST'])
def register_user():
    """
    Register a new user.
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not all(k in data for k in ['email', 'password', 'full_name']):
            return jsonify(create_response(
                success=False,
                message="Missing required fields: email, password, full_name",
                data=None
            )), 400

        driver = get_driver()

        # Use the create_user method from db_driver
        user = driver.create_user(
            email=data['email'],
            password=data['password'],
            full_name=data['full_name']
        )

        return jsonify(create_response(
            data={"user_id": user["user_id"]},
            message="User registered successfully"
        )), 201

    except Exception as e:
        logger.error(f"User registration error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Registration failed: {str(e)}",
            data=None
        )), 500

@app.route('/users/login', methods=['POST'])
def login_user():
    """
    Login a user and return a JWT token.
    """
    try:
        data = request.get_json()

        # Validate required fields
        if not all(k in data for k in ['email', 'password']):
            return jsonify(create_response(
                success=False,
                message="Missing required fields: email, password",
                data=None
            )), 400

        driver = get_driver()

        # Sign in user with Supabase Auth
        auth_response = driver.client.auth.sign_in_with_password({
            "email": data['email'],
            "password": data['password'],
        })

        if not auth_response.user:
            return jsonify(create_response(
                success=False,
                message="Invalid credentials",
                data=None
            )), 401

        # Get user profile using the get_user method
        try:
            user_profile = driver.get_user(auth_response.user.id)
        except Exception:
            # If user profile doesn't exist in our database, create a minimal one
            user_profile = {
                "id": auth_response.user.id,
                "email": auth_response.user.email
            }

        return jsonify(create_response(
            data={
                "user": user_profile,
                "access_token": auth_response.session.access_token,
                "token_type": "bearer",
                "expires_at": auth_response.session.expires_at,
            },
            message="Login successful"
        )), 200

    except Exception as e:
        logger.error(f"User login error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message="Invalid credentials",
            data=None
        )), 401

@app.route('/users/me', methods=['GET'])
@token_required
def get_current_user_profile():
    """
    Get the current user's profile.
    """
    try:
        driver = get_driver()
        # Get user data from Supabase auth
        user_data = g.user

        # Get user profile using the get_user method
        try:
            user_profile = driver.get_user(user_data.id)
        except Exception:
            # If user profile doesn't exist in our database, create a minimal one
            user_profile = {
                "id": user_data.id,
                "email": user_data.email,
                "created_at": user_data.created_at
            }

        return jsonify(create_response(
            data=user_profile,
            message="User profile retrieved successfully"
        )), 200

    except Exception as e:
        logger.error(f"Get user profile error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to retrieve user profile: {str(e)}",
            data=None
        )), 500

# Business Management Endpoints

@app.route('/businesses', methods=['POST'])
@token_required
def create_business():
    """
    Create a new business profile.
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['name', 'address', 'contact_email', 'contact_phone']
        if not all(k in data for k in required_fields):
            return jsonify(create_response(
                success=False,
                message=f"Missing required fields: {', '.join(required_fields)}",
                data=None
            )), 400

        driver = get_driver()

        # Use the create_business method from db_driver
        business = driver.create_business(
            user_id=g.user.id,
            business_data=data,
            app_name=APP_CONNECTO
        )

        return jsonify(create_response(
            data=business,
            message="Business profile created successfully"
        )), 201

    except Exception as e:
        logger.error(f"Create business error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to create business profile: {str(e)}",
            data=None
        )), 500

@app.route('/businesses/<business_id>', methods=['GET'])
@token_required
def get_business(business_id):
    """
    Get a business profile by ID.
    """
    try:
        driver = get_driver()

        # Use the get_business method from db_driver
        try:
            business = driver.get_business(business_id)

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            return jsonify(create_response(
                data=business,
                message="Business profile retrieved successfully"
            )), 200

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Get business error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to retrieve business profile: {str(e)}",
            data=None
        )), 500

@app.route('/businesses/<business_id>', methods=['PUT'])
@token_required
def update_business(business_id):
    """
    Update a business profile by ID.
    """
    try:
        data = request.get_json()
        driver = get_driver()

        # First check if business exists and belongs to user
        try:
            business = driver.get_business(business_id)

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            # Use the update_business method from db_driver
            updated_business = driver.update_business(business_id, data)

            return jsonify(create_response(
                data=updated_business,
                message="Business profile updated successfully"
            )), 200

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Update business error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to update business profile: {str(e)}",
            data=None
        )), 500

@app.route('/businesses/user', methods=['GET'])
@token_required
def get_user_businesses():
    """
    Get all businesses belonging to the current user.
    """
    try:
        driver = get_driver()

        # Get businesses from database using the get_user_businesses method
        # Note: The method might not exist in the driver, so we'll handle that case
        try:
            # Check if the method exists
            if hasattr(driver, 'get_user_businesses'):
                businesses = driver.get_user_businesses(g.user.id)
            else:
                # Fallback to direct query
                response = driver.client.table("restaurants").select("*").eq("user_id", g.user.id).execute()
                businesses = response.data

            return jsonify(create_response(
                data=businesses,
                message="Businesses retrieved successfully"
            )), 200

        except Exception as e:
            # If the method doesn't exist or fails, fall back to direct query
            response = driver.client.table("restaurants").select("*").eq("user_id", g.user.id).execute()

            return jsonify(create_response(
                data=response.data,
                message="Businesses retrieved successfully"
            )), 200

    except Exception as e:
        logger.error(f"Get user businesses error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to retrieve businesses: {str(e)}",
            data=None
        )), 500

# Agent Configuration Endpoints

@app.route('/agent-configs/<business_id>', methods=['GET'])
@token_required
def get_agent_config(business_id):
    """
    Get agent configuration for a business.
    """
    try:
        driver = get_driver()

        # First check if business exists and belongs to user
        try:
            business = driver.get_business(business_id)

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            # Get agent config using the get_or_create_agent_config method
            default_config = {
                "voice": "coral",
                "temperature": 0.8,
                "demo_mode": False
            }

            config = driver.get_or_create_agent_config(business_id, default_config)

            return jsonify(create_response(
                data=config,
                message="Agent configuration retrieved successfully"
            )), 200

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Get agent config error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to retrieve agent configuration: {str(e)}",
            data=None
        )), 500

@app.route('/agent-configs/<business_id>', methods=['POST'])
@token_required
def create_or_update_agent_config(business_id):
    """
    Create or update agent configuration for a business.
    """
    try:
        data = request.get_json()
        driver = get_driver()

        # First check if business exists and belongs to user
        try:
            business = driver.get_business(business_id)

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            # Validate config data
            if 'config' not in data:
                return jsonify(create_response(
                    success=False,
                    message="Missing required field: config",
                    data=None
                )), 400

            # Use the update_agent_config_by_business method
            config = driver.update_agent_config_by_business(business_id, data['config'])

            return jsonify(create_response(
                data=config,
                message="Agent configuration updated successfully"
            )), 200

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Update agent config error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to update agent configuration: {str(e)}",
            data=None
        )), 500

# Call Log Endpoints

@app.route('/call-logs/<business_id>', methods=['GET'])
@token_required
def get_call_logs(business_id):
    """
    Get call logs for a business.
    """
    try:
        driver = get_driver()

        # First check if business exists and belongs to user
        try:
            business = driver.get_business(business_id)

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            # Get pagination parameters
            limit = request.args.get('limit', default=10, type=int)

            # Use the get_call_logs method
            call_logs = driver.get_call_logs(business_id, limit=limit)

            return jsonify(create_response(
                data=call_logs,
                message="Call logs retrieved successfully"
            )), 200

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Get call logs error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to retrieve call logs: {str(e)}",
            data=None
        )), 500

@app.route('/call-logs/<business_id>/stats', methods=['GET'])
@token_required
def get_call_stats(business_id):
    """
    Get call statistics for a business.
    """
    try:
        driver = get_driver()

        # First check if business exists and belongs to user
        try:
            business = driver.get_business(business_id)

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            # Get days parameter
            days = request.args.get('days', default=30, type=int)

            # Use the get_business_stats function
            stats = get_business_stats(business_id, days)

            return jsonify(create_response(
                data=stats,
                message="Call statistics retrieved successfully"
            )), 200

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Get call stats error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to retrieve call statistics: {str(e)}",
            data=None
        )), 500

@app.route('/call-logs', methods=['POST'])
@token_required
def create_call_log():
    """
    Create a new call log.
    """
    try:
        data = request.get_json()

        # Validate required fields
        if 'business_id' not in data:
            return jsonify(create_response(
                success=False,
                message="Missing required field: business_id",
                data=None
            )), 400

        driver = get_driver()

        # First check if business exists and belongs to user
        try:
            business = driver.get_business(data['business_id'])

            # Check if user owns this business
            if business["user_id"] != g.user.id:
                return jsonify(create_response(
                    success=False,
                    message="Unauthorized access to business profile",
                    data=None
                )), 403

            # Use the log_call method
            call_data = {
                "caller_number": data.get('caller_number'),
                "duration": data.get('duration'),
                "transcript": data.get('transcript'),
                "summary": data.get('summary')
            }

            # Filter out None values
            call_data = {k: v for k, v in call_data.items() if v is not None}

            # Log the call
            call_log = driver.log_call(data['business_id'], call_data)

            return jsonify(create_response(
                data=call_log,
                message="Call log created successfully"
            )), 201

        except Exception as e:
            if "not found" in str(e).lower():
                return jsonify(create_response(
                    success=False,
                    message="Business not found",
                    data=None
                )), 404
            else:
                raise

    except Exception as e:
        logger.error(f"Create call log error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to create call log: {str(e)}",
            data=None
        )), 500

# Voice Preview Endpoint

@app.route('/preview', methods=['POST'])
def generate_voice_preview():
    """
    Generate a voice preview for a business.
    """
    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['business_name', 'business_type']
        if not all(k in data for k in required_fields):
            return jsonify(create_response(
                success=False,
                message=f"Missing required fields: {', '.join(required_fields)}",
                data=None
            )), 400

        # Import necessary modules for voice generation
        from prompt import get_template
        import os
        import uuid

        # Get the template for this business type
        template = get_template(data['business_type'], data['business_name'])
        welcome_message = data.get('welcome_message') or template["WELCOME_MESSAGE"]

        # Format the welcome message with the business name
        welcome_message = welcome_message.format(business_name=data['business_name'])

        # Generate a unique filename for this preview
        preview_id = str(uuid.uuid4())
        audio_filename = f"{preview_id}.mp3"
        audio_path = os.path.join("static", "audio", audio_filename)

        # Use text-to-speech to generate the audio file
        # This is a placeholder - in a real implementation, you would use a TTS service
        # For example, you might use OpenAI's TTS API or another service

        # For demonstration purposes, we'll just create a dummy file
        # In a real implementation, replace this with actual TTS code
        try:
            # Check if OpenAI API key is available
            openai_api_key = os.environ.get("OPENAI_API_KEY")
            if openai_api_key:
                # Use OpenAI's TTS API
                import openai
                client = openai.OpenAI(api_key=openai_api_key)

                response = client.audio.speech.create(
                    model="tts-1",
                    voice="shimmer",  # Options: alloy, echo, fable, onyx, nova, shimmer
                    input=welcome_message
                )

                # Save the audio file
                with open(audio_path, "wb") as f:
                    f.write(response.content)
            else:
                # Fallback to a dummy file if OpenAI API key is not available
                with open(audio_path, "wb") as f:
                    f.write(b"Dummy audio file")
                logger.warning("OpenAI API key not found. Created dummy audio file.")
        except Exception as e:
            logger.error(f"TTS generation error: {str(e)}")
            # Create a dummy file as fallback
            with open(audio_path, "wb") as f:
                f.write(b"Dummy audio file")

        # Return the URL to the generated audio file
        audio_url = f"/static/audio/{audio_filename}"

        return jsonify(create_response(
            data={
                "preview_id": preview_id,
                "audio_url": audio_url,
                "welcome_message": welcome_message,
                "business_name": data['business_name'],
                "business_type": data['business_type']
            },
            message="Voice preview generated successfully"
        )), 200

    except Exception as e:
        logger.error(f"Voice preview generation error: {str(e)}")
        return jsonify(create_response(
            success=False,
            message=f"Failed to generate voice preview: {str(e)}",
            data=None
        )), 500

# Main entry point
if __name__ == '__main__':
    # Run the Flask app
    app.run(host='0.0.0.0', port=5000, debug=True)
