"""
Example of integrating the Supabase database driver with the Connecto agent.

This script demonstrates how to:
1. Configure the agent using database settings
2. Log calls to the database
3. Work with existing restaurant profiles
4. Register restaurants with the Connecto platform

Usage:
    python db_integration.py
"""

import os
import uuid
from dotenv import load_dotenv
import sys

# Add parent directory to path to import from Conector
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db_driver import (
    get_driver,
    configure_agent_from_db,
    log_agent_call,
    register_business_for_connecto,
    get_business_stats,
    APP_CONNECTO,
    APP_RESTAURANT_MGMT
)

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
load_dotenv(dotenv_path)


def create_demo_user():
    """Create a demo user account."""
    driver = get_driver()

    # Create a user
    try:
        user = driver.create_user(
            email="<EMAIL>",
            password="password123",
            full_name="Demo User"
        )
        print(f"Created user: {user['full_name']} ({user['email']})")
        return user["user_id"]
    except Exception as e:
        print(f"Error creating user: {e}")
        # Try to get existing user
        try:
            # This is a simplified example - in a real app, you'd need proper authentication
            response = driver.client.table("users").select("*").eq("email", "<EMAIL>").execute()
            if len(response.data) > 0:
                user = response.data[0]
                print(f"Using existing user: {user['full_name']} ({user['email']})")
                return user["id"]
            else:
                print("Could not create or find user")
                # Create a temporary user ID for demo purposes
                return str(uuid.uuid4())
        except Exception as e:
            print(f"Error finding user: {e}")
            # Create a temporary user ID for demo purposes
            return str(uuid.uuid4())


def find_existing_restaurant():
    """Find an existing restaurant in the database."""
    driver = get_driver()

    try:
        # Query for existing restaurants
        response = driver.client.from_("restaurants").select("*").limit(5).execute()

        if len(response.data) > 0:
            # Return the first restaurant found
            restaurant = response.data[0]
            print(f"Found existing restaurant: {restaurant['name']} (ID: {restaurant['id']})")
            return restaurant
        else:
            print("No existing restaurants found")
            return None
    except Exception as e:
        print(f"Error finding restaurants: {e}")
        return None


def demo_multi_app_integration():
    """Demonstrate integration with multiple applications."""
    driver = get_driver()
    user_id = create_demo_user()

    print("\nDemonstrating Multi-Application Integration")
    print("===========================================")

    # 1. Find an existing restaurant or create a new one
    print("\n1. Finding an existing restaurant...")
    restaurant = find_existing_restaurant()

    if not restaurant:
        print("Creating a new restaurant...")
        try:
            restaurant = driver.create_business(
                user_id=user_id,
                business_data={
                    "name": "Tasty Bites Restaurant",
                    "type": "restaurant",
                    "contact_phone": "************",
                    "contact_email": "<EMAIL>",
                    "address": "123 Main St, Anytown, USA",
                    "opening_hours": {"Mon-Fri": "9am-9pm", "Sat-Sun": "10am-10pm"}
                },
                app_name=APP_RESTAURANT_MGMT
            )
            print(f"Created restaurant: {restaurant['name']} (ID: {restaurant['id']})")
        except Exception as e:
            print(f"Error creating restaurant: {e}")
            return None

    try:
        # 2. Register the restaurant with the Restaurant Management app
        print("\n2. Registering the restaurant with Restaurant Management app...")
        driver.register_business_with_app(
            restaurant["id"],
            driver.get_application_id(APP_RESTAURANT_MGMT),
            {
                "pos_system": "Square",
                "menu_items": 45,
                "reservation_system": "OpenTable"
            }
        )
        print(f"Registered restaurant with Restaurant Management app")

        # 3. Register the restaurant with Connecto
        print("\n3. Registering the restaurant with Connecto...")
        register_business_for_connecto(
            restaurant["id"],
            {
                "welcome_message": f"Custom welcome message for {restaurant['name']}",
                "business_hours_handling": "strict",
                "call_forwarding": "enabled"
            }
        )
        print(f"Registered restaurant with Connecto")

        # 4. Configure the Connecto agent
        print("\n4. Configuring Connecto agent...")
        config = configure_agent_from_db(business_id=restaurant["id"], app_name=APP_CONNECTO)
        print("Agent Configuration:")
        print(f"Business ID: {config['business_id']}")
        print(f"Business Type: {config.get('business_type', 'restaurant')}")
        print(f"Business Name: {config['business_name']}")
        print(f"App Settings: {config['app_settings']}")
        print(f"Agent Config: {config['agent_config']}")

        # 5. Log a call with Connecto
        print("\n5. Logging a call with Connecto...")
        call_log = log_agent_call(
            business_id=restaurant["id"],
            caller_number="************",
            duration=120,
            transcript="Customer: Hi, I'd like to make a reservation for tonight.\nAgent: I'd be happy to help with that. How many people will be in your party?",
            summary="Customer called to make a dinner reservation for 4 people at 7:30 PM."
        )
        print(f"Call logged successfully with ID: {call_log['id']}")

        # 6. Get business stats from Connecto
        print("\n6. Getting business stats from Connecto...")
        stats = get_business_stats(restaurant["id"])
        print(f"Total calls: {stats['call_stats']['total_calls']}")
        print(f"Recent calls: {len(stats['recent_calls'])}")

        # 7. Demonstrate cross-application data access
        print("\n7. Demonstrating cross-application data access...")
        business_with_settings = driver.get_business_with_app_settings(restaurant["id"], APP_RESTAURANT_MGMT)
        print(f"Restaurant Management settings: {business_with_settings.get('app_settings', {})}")

        business_with_settings = driver.get_business_with_app_settings(restaurant["id"], APP_CONNECTO)
        print(f"Connecto settings: {business_with_settings.get('app_settings', {})}")

        return restaurant["id"]

    except Exception as e:
        print(f"Error in multi-app demo: {e}")
        return None


def demo_agent_integration(business_id=None):
    """Demonstrate how to integrate the database with the agent."""
    if not business_id:
        # Find an existing restaurant
        restaurant = find_existing_restaurant()
        if restaurant:
            business_id = restaurant["id"]
            print(f"Using existing restaurant: {restaurant['name']}")
        else:
            print("No restaurant ID provided and no existing restaurants found")
            print("Creating a simple configuration with default values")
            config = configure_agent_from_db(
                business_name="Demo Restaurant",
                app_name=APP_CONNECTO
            )
            print("\nDefault Agent Configuration:")
            print(f"Business ID: {config['business_id']}")
            print(f"Business Name: {config['business_name']}")
            print(f"App Settings: {config['app_settings']}")
            print(f"Agent Config: {config['agent_config']}")
            print("\nCannot log calls without a business ID")
            return

    print(f"Configuring agent for restaurant ID: {business_id}")
    config = configure_agent_from_db(business_id=business_id, app_name=APP_CONNECTO)

    print("\nAgent Configuration:")
    print(f"Business ID: {config['business_id']}")
    print(f"Business Name: {config['business_name']}")
    print(f"Business Phone: {config.get('business_phone', 'N/A')}")
    print(f"Business Email: {config.get('business_email', 'N/A')}")
    print(f"Business Address: {config.get('business_address', 'N/A')}")
    print(f"App Settings: {config['app_settings']}")
    print(f"Agent Config: {config['agent_config']}")

    # In a real implementation, you would use this configuration to set up your agent
    # For example:
    # agent = ConnectoAgent(
    #     business_name=config['business_name'],
    #     demo_mode=config['agent_config'].get('demo_mode', False)
    # )

    # Log a simulated call
    print("\nLogging a simulated call...")
    call_log = log_agent_call(
        business_id=config['business_id'],
        caller_number="************",
        duration=120,
        transcript="Customer: Hi, I'd like to make a reservation for tonight.\nAgent: I'd be happy to help with that. How many people will be in your party?",
        summary="Customer called to make a dinner reservation for 4 people at 7:30 PM."
    )
    print(f"Call logged successfully with ID: {call_log['id']}")

    # Get call logs
    driver = get_driver()
    logs = driver.get_call_logs(business_id=config['business_id'], limit=5)
    print(f"\nRecent call logs ({len(logs)}):")
    for log in logs:
        print(f"- {log['timestamp']}: {log.get('summary', 'No summary')} (Duration: {log.get('duration', 'N/A')}s)")


def main():
    """Main function to run the demo."""
    print("Connecto Database Integration Example")
    print("=====================================")

    # Demonstrate multi-application integration
    business_id = demo_multi_app_integration()

    # Demonstrate agent integration
    if not business_id:
        print("\nFalling back to simple agent integration demo...")
        demo_agent_integration()
    else:
        print("\nDemonstrating agent integration with existing business...")
        demo_agent_integration(business_id)


if __name__ == "__main__":
    main()
