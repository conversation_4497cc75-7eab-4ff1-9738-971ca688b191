from __future__ import annotations
import os
import sys
import argparse
import asyncio
from dotenv import load_dotenv

from livekit import agents
from livekit.agents import AgentSession, Agent, RoomInputOptions
from livekit.plugins import openai



try:
    from livekit.plugins import noise_cancellation
    NOISE_CANCELLATION_AVAILABLE = True
except ImportError:
    NOISE_CANCELLATION_AVAILABLE = False
    print("Noise cancellation plugin not available. Install with: pip install livekit-plugins-noise-cancellation")
from prompt import get_template, BUSINESS_TEMPLATES



dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path)
print(f"Loaded environment variables from: {dotenv_path}")


# Default business settings
DEFAULT_BUSINESS_TYPE = "base"
DEFAULT_BUSINESS_NAME = "SME Analytica"


class ConnectoAgent(Agent):
    def __init__(self, business_type=DEFAULT_BUSINESS_TYPE, business_name=DEFAULT_BUSINESS_NAME,
                 demo_mode=False, business_info=None):
        """
        Initialize a Connecto AI Voice Receptionist agent.

        Args:
            business_type (str): Type of business (e.g., 'auto_service', 'restaurant')
            business_name (str): Name of the business
            demo_mode (bool): Whether the agent is running in demo/preview mode
            business_info (dict): Additional business information for customization
        """


        template = get_template(business_type, business_name, business_info)
        instructions = template["INSTRUCTIONS"]

        if demo_mode:
            demo_prefix = """
            This is a preview of the Connecto AI Voice Receptionist.
            You are demonstrating how the AI would sound for this business type.
            If asked about being a demo or preview, acknowledge that you are a preview
            and explain that the full version would be customized for their specific business.
            """
            instructions = demo_prefix + instructions


        super().__init__(instructions=instructions)


        self.business_type = business_type
        self.business_name = business_name
        self.welcome_message = template["WELCOME_MESSAGE"]
        self.demo_mode = demo_mode
        self.business_info = business_info or {}


async def entrypoint(ctx: agents.JobContext):
    business_type = os.environ.get("BUSINESS_TYPE", DEFAULT_BUSINESS_TYPE)
    business_name = os.environ.get("BUSINESS_NAME", DEFAULT_BUSINESS_NAME)


    demo_mode = os.environ.get("DEMO_MODE", "false").lower() == "true"


    # Collect business information from environment variables
    business_info = {}

    # Basic business information
    for key in ["BUSINESS_PHONE", "BUSINESS_EMAIL", "BUSINESS_ADDRESS", "BUSINESS_HOURS"]:
        if value := os.environ.get(key):
            info_key = key.replace("BUSINESS_", "").lower()
            business_info[info_key] = value

    # Add SME Analytica information
    business_info["sme_analytica"] = {
        "company_name": "SME Analytica",
        "company_description": "AI-powered B2B SaaS startup based in Valencia, Spain",
        "mission": "Empowering SMEs with affordable, enterprise-grade AI technology that enhances efficiency, improves customer experience, and drives growth through data-informed decision making",
        "website": "https://smeanalytica.dev",
        "restaurant_website": "https://restaurants.smeanalytica.dev",
        "email": "<EMAIL>",
        "founder": "Ola Yinka",
        "founded": "2024",
        "location": "Valencia, Spain",
        "status": "Testing phase, seeking strategic partners and investors",
        "services": [
            "Connecto AI Voice Receptionist",
            "Restaurant Management System with Dynamic Pricing",
            "Operational Analytics",
            "Customer Behavior Insights",
            "Real-Time Traffic Tracking",
            "QR-Based Table Ordering",
            "Menu Personalization",
            "Order Routing",
            "Loyalty Incentives",
            "Mobile Application (currently on TestFlight for iOS)"
        ],
        "mobile_app": {
            "available": True,
            "platform": "iOS",
            "status": "TestFlight beta testing",
            "features": "Business analytics and management tools on-the-go"
        }
    }

    if business_type == "auto_service":
        business_info["services"] = {
            "oil_change_price": os.environ.get("AUTO_OIL_CHANGE_PRICE", "€49.99-€89.99"),
            "tire_rotation_price": os.environ.get("AUTO_TIRE_ROTATION_PRICE", "€25"),
            "brake_service_price": os.environ.get("AUTO_BRAKE_SERVICE_PRICE", "requires inspection for accurate quote"),
            "diagnostic_fee": os.environ.get("AUTO_DIAGNOSTIC_FEE", "€89.99")
        }
    elif business_type == "barbershop":
        business_info["services"] = {
            "haircut_price": os.environ.get("BARBERSHOP_HAIRCUT_PRICE", "€25-€35"),
            "haircut_beard_price": os.environ.get("BARBERSHOP_HAIRCUT_BEARD_PRICE", "€40-€50"),
            "hot_towel_price": os.environ.get("BARBERSHOP_HOT_TOWEL_PRICE", "€30"),
            "kids_haircut_price": os.environ.get("BARBERSHOP_KIDS_HAIRCUT_PRICE", "€20"),
            "senior_haircut_price": os.environ.get("BARBERSHOP_SENIOR_HAIRCUT_PRICE", "€20")
        }

    # Print business information
    if demo_mode:
        print(f"Starting Connecto AI Voice Receptionist DEMO for: {business_name}")
        print(f"Business type: {business_type} (DEMO MODE)")
    else:
        print(f"Starting Connecto AI Voice Receptionist for: {business_name}")
        print(f"Business type: {business_type}")

    # Check for OpenAI API key
    openai_api_key = os.environ.get("OPENAI_API_KEY")
    if not openai_api_key:
        print("ERROR: OpenAI API key not found!")
        print("Please set the OPENAI_API_KEY environment variable.")
        print("Example: export OPENAI_API_KEY=your-api-key-here")
        return
    else:
        print(f"Found OpenAI API key: {openai_api_key[:8]}...{openai_api_key[-4:]}")

    # Print environment variables for debugging
    print("Environment variables:")
    for key in ["LIVEKIT_URL", "LIVEKIT_API_KEY", "LIVEKIT_API_SECRET", "OPENAI_API_KEY"]:
        value = os.environ.get(key, "Not set")
        if key == "OPENAI_API_KEY" and value != "Not set":
            print(f"  {key}: {value[:8]}...{value[-4:]}")
        elif key == "LIVEKIT_API_SECRET" and value != "Not set":
            print(f"  {key}: {value[:8]}...{value[-4:]}")
        else:
            print(f"  {key}: {value}")


    agent = ConnectoAgent(
        business_type=business_type,
        business_name=business_name,
        demo_mode=demo_mode,
        business_info=business_info
    )


    session = AgentSession(
        llm=openai.realtime.RealtimeModel(
            voice="coral",  # Options: alloy, echo, fable, onyx, nova, shimmer
            temperature=0.8
        )
    )

    # Handle user input events
    @session.on("user_input_transcribed")
    def on_user_input_transcribed(event):
        print(f"User said: {event.transcript}")

    # Handle conversation events
    @session.on("conversation_item_added")
    def on_conversation_item_added(event):
        if event.item.role == "assistant":
            print(f"Assistant said: {event.item.content}")

    if NOISE_CANCELLATION_AVAILABLE:
        try:
            room_options = RoomInputOptions(
                noise_cancellation=noise_cancellation.BVC() # type: ignore
            )
            print("Noise cancellation enabled")
        except Exception as e:
            print(f"Error configuring noise cancellation: {e}")
            room_options = RoomInputOptions()
    else:
        print("Noise cancellation not available")
        room_options = RoomInputOptions()


    print("Starting agent session...")
    await session.start(
        room=ctx.room,
        agent=agent,
        room_input_options=room_options
    )


    print("Connecting to room...")
    await ctx.connect()
    print("Connected to room successfully!")


    if agent.demo_mode:
        demo_welcome = f"This is a preview of the Connecto AI Voice Receptionist for {agent.business_name}. {agent.welcome_message}"
        print(f"Generating demo welcome message: '{demo_welcome}'")
        await session.generate_reply(
            instructions=f"Greet the user with: {demo_welcome}"
        )
    else:
        print(f"Generating welcome message: '{agent.welcome_message}'")
        await session.generate_reply(
            instructions=f"Greet the user with: {agent.welcome_message}"
        )

    print("Agent is now ready to interact!")

    try:
        print("Agent is running. Press Ctrl+C to stop.")

        while True:
            await asyncio.sleep(1)

    except KeyboardInterrupt:
        print("Keyboard interrupt received")
    except Exception as e:
        print(f"Error in session: {e}")
    finally:
        print("Shutting down agent...")
        print("Agent shutdown complete")

def list_business_types():
    """List all available business types."""
    print("Available business types:")
    for business_type in sorted(BUSINESS_TEMPLATES.keys()):
        print(f"  - {business_type}")


def parse_arguments():
    """
    Parse command line arguments for business type and name.
    This function sets environment variables based on command-line arguments.
    """
    business_type = os.environ.get("BUSINESS_TYPE", DEFAULT_BUSINESS_TYPE)
    business_name = os.environ.get("BUSINESS_NAME", DEFAULT_BUSINESS_NAME)
    demo_mode = os.environ.get("DEMO_MODE", "false").lower() == "true"



    i = 1
    while i < len(sys.argv):
        if i < len(sys.argv) - 1:  # Make sure we have a next argument
            if sys.argv[i] == "--business-type" and not sys.argv[i+1].startswith("--"):
                business_type = sys.argv[i+1]
                os.environ["BUSINESS_TYPE"] = business_type
                i += 2
                continue

            elif sys.argv[i] == "--business-name" and not sys.argv[i+1].startswith("--"):
                business_name = sys.argv[i+1]
                os.environ["BUSINESS_NAME"] = business_name
                i += 2
                continue

            elif sys.argv[i] == "--api-key" and not sys.argv[i+1].startswith("--"):
                os.environ["OPENAI_API_KEY"] = sys.argv[i+1]
                print(f"Using OpenAI API key from command line")
                i += 2
                continue

            elif sys.argv[i] == "--business-phone" and not sys.argv[i+1].startswith("--"):
                os.environ["BUSINESS_PHONE"] = sys.argv[i+1]
                i += 2
                continue

            elif sys.argv[i] == "--business-email" and not sys.argv[i+1].startswith("--"):
                os.environ["BUSINESS_EMAIL"] = sys.argv[i+1]
                i += 2
                continue

            elif sys.argv[i] == "--business-address" and not sys.argv[i+1].startswith("--"):
                os.environ["BUSINESS_ADDRESS"] = sys.argv[i+1]
                i += 2
                continue

            elif sys.argv[i] == "--business-hours" and not sys.argv[i+1].startswith("--"):
                os.environ["BUSINESS_HOURS"] = sys.argv[i+1]
                i += 2
                continue

            # Auto service specific args
            elif sys.argv[i] == "--auto-oil-change-price" and not sys.argv[i+1].startswith("--"):
                os.environ["AUTO_OIL_CHANGE_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--auto-tire-rotation-price" and not sys.argv[i+1].startswith("--"):
                os.environ["AUTO_TIRE_ROTATION_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--auto-brake-service-price" and not sys.argv[i+1].startswith("--"):
                os.environ["AUTO_BRAKE_SERVICE_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--auto-diagnostic-fee" and not sys.argv[i+1].startswith("--"):
                os.environ["AUTO_DIAGNOSTIC_FEE"] = sys.argv[i+1]
                i += 2
                continue

            # Barbershop specific args
            elif sys.argv[i] == "--barbershop-haircut-price" and not sys.argv[i+1].startswith("--"):
                os.environ["BARBERSHOP_HAIRCUT_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--barbershop-haircut-beard-price" and not sys.argv[i+1].startswith("--"):
                os.environ["BARBERSHOP_HAIRCUT_BEARD_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--barbershop-hot-towel-price" and not sys.argv[i+1].startswith("--"):
                os.environ["BARBERSHOP_HOT_TOWEL_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--barbershop-kids-haircut-price" and not sys.argv[i+1].startswith("--"):
                os.environ["BARBERSHOP_KIDS_HAIRCUT_PRICE"] = sys.argv[i+1]
                i += 2
                continue
            elif sys.argv[i] == "--barbershop-senior-haircut-price" and not sys.argv[i+1].startswith("--"):
                os.environ["BARBERSHOP_SENIOR_HAIRCUT_PRICE"] = sys.argv[i+1]
                i += 2
                continue

        if sys.argv[i] == "--demo":
            demo_mode = True
            os.environ["DEMO_MODE"] = "true"
            i += 1
            continue

        elif sys.argv[i] == "--sme-analytica":
            # Set to SME Analytica mode
            business_type = "base"
            business_name = "SME Analytica"
            os.environ["BUSINESS_TYPE"] = business_type
            os.environ["BUSINESS_NAME"] = business_name
            i += 1
            continue

        elif sys.argv[i] == "--list-types":
            list_business_types()
            exit(0)

        i += 1

    if business_type not in BUSINESS_TEMPLATES and business_type != "base":
        print(f"Warning: Unknown business type '{business_type}'")
        print("Using base template instead. Available types are:")
        list_business_types()
        print("  - base (generic template)")
        business_type = "base"
        os.environ["BUSINESS_TYPE"] = business_type

    if demo_mode:
        print(f"Starting Connecto AI Voice Receptionist DEMO for: {business_name}")
        print(f"Business type: {business_type} (DEMO MODE)")
    else:
        print(f"Starting Connecto AI Voice Receptionist for: {business_name}")
        print(f"Business type: {business_type}")

    return argparse.Namespace(
        business_type=business_type,
        business_name=business_name,
        demo=demo_mode
    )

if __name__ == "__main__":
    parse_arguments()
    original_argv = sys.argv.copy()
    filtered_args = []
    skip_next = False
    custom_args = [
        "--business-type", "--business-name", "--api-key", "--demo", "--sme-analytica",
        "--business-phone", "--business-email", "--business-address", "--business-hours",
        "--list-types",
        # Auto service specific args
        "--auto-oil-change-price", "--auto-tire-rotation-price",
        "--auto-brake-service-price", "--auto-diagnostic-fee",
        # Barbershop specific args
        "--barbershop-haircut-price", "--barbershop-haircut-beard-price",
        "--barbershop-hot-towel-price", "--barbershop-kids-haircut-price",
        "--barbershop-senior-haircut-price"
    ]
    for i, arg in enumerate(original_argv):
        if skip_next:
            skip_next = False
            continue

        if arg in custom_args:
            if arg != "--demo" and arg != "--list-types" and i < len(original_argv) - 1:
                skip_next = True
            continue
        filtered_args.append(arg)
    sys.argv = filtered_args

    agents.cli.run_app(agents.WorkerOptions(entrypoint_fnc=entrypoint))
