"""
Supabase Database Driver for Connecto AI Voice Receptionist.

This module handles all database operations for the Connecto platform, including:
- User authentication and management
- Business profile storage and retrieval
- Agent configuration settings
- Usage statistics and logs

The driver uses Supabase as the backend database service.
"""

import os
import json
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
dotenv_path = os.path.join(os.path.dirname(__file__), '.env')
load_dotenv(dotenv_path)

# Supabase configuration
SUPABASE_URL = os.environ.get("SUPABASE_URL")
SUPABASE_KEY = os.environ.get("SUPABASE_KEY")

# Schema names
SCHEMA_PUBLIC = "public"
SCHEMA_CONNECTO = "connecto"

# Table names (without schema prefix for Supabase client)
TABLE_APPLICATIONS = "applications"
TABLE_USERS = "user_profiles"  # Using existing user_profiles table
TABLE_RESTAURANTS = "restaurants"  # Using existing restaurants table
TABLE_BUSINESS_APPLICATIONS = "business_applications"
TABLE_AGENT_CONFIGS = "agent_configs"  # Supabase will handle schema automatically
TABLE_CALL_LOGS = "call_logs"  # Supabase will handle schema automatically

# Application constants
APP_CONNECTO = "connecto"
APP_RESTAURANT_MGMT = "restaurant_mgmt"

class SupabaseDriver:
    """
    Supabase database driver for Connecto AI Voice Receptionist.
    Handles all database operations including CRUD for users, businesses, and agent configurations.
    """

    def __init__(self, url: Optional[str] = None, key: Optional[str] = None):
        """
        Initialize the Supabase client.

        Args:
            url: Supabase project URL (optional, defaults to environment variable)
            key: Supabase API key (optional, defaults to environment variable)
        """
        self.url = url or SUPABASE_URL
        self.key = key or SUPABASE_KEY

        if not self.url or not self.key:
            raise ValueError(
                "Supabase URL and API key must be provided either as arguments "
                "or as environment variables (SUPABASE_URL, SUPABASE_KEY)"
            )

        # Initialize Supabase client
        self.client = create_client(self.url, self.key)

    # User Management Functions

    def create_user(self, email: str, password: str, full_name: str) -> Dict[str, Any]:
        """
        Create a new user account.

        Args:
            email: User's email address
            password: User's password
            full_name: User's full name

        Returns:
            Dict containing user information
        """
        try:
            # Create user in Supabase Auth
            self.client.auth.sign_up({
                "email": email,
                "password": password
            })

            # Generate a UUID for the user
            import uuid
            user_id = str(uuid.uuid4())

            # Check if user profile already exists
            existing_profile = self.client.table(TABLE_USERS).select("*").eq("id", user_id).execute()

            if len(existing_profile.data) > 0:
                return {
                    "user_id": user_id,
                    "email": email,
                    "full_name": existing_profile.data[0].get("full_name", full_name)
                }

            # Add user to user_profiles table with additional information
            user_data = {
                "id": user_id,
                "email": email,
                "full_name": full_name,
                "created_at": "now()",
                "updated_at": "now()"
            }

            self.client.table(TABLE_USERS).insert(user_data).execute()

            return {"user_id": user_id, "email": email, "full_name": full_name}

        except Exception as e:
            raise Exception(f"Failed to create user: {str(e)}")

    def get_user(self, user_id: str) -> Dict[str, Any]:
        """
        Get user information by ID.

        Args:
            user_id: The user's unique ID

        Returns:
            Dict containing user information
        """
        try:
            response = self.client.table(TABLE_USERS).select("*").eq("id", user_id).execute()

            if len(response.data) == 0:
                raise Exception(f"User with ID {user_id} not found")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to get user: {str(e)}")

    def update_user(self, user_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update user information.

        Args:
            user_id: The user's unique ID
            data: Dict containing fields to update

        Returns:
            Dict containing updated user information
        """
        try:
            # Add updated_at timestamp
            update_data = {**data, "updated_at": "now()"}

            response = self.client.table(TABLE_USERS).update(update_data).eq("id", user_id).execute()

            if len(response.data) == 0:
                raise Exception(f"User with ID {user_id} not found")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to update user: {str(e)}")

    def delete_user(self, user_id: str) -> bool:
        """
        Delete a user account.

        Args:
            user_id: The user's unique ID

        Returns:
            True if successful
        """
        try:
            # Delete from users table
            self.client.table(TABLE_USERS).delete().eq("id", user_id).execute()

            # Delete user from Supabase Auth
            self.client.auth.admin.delete_user(user_id)

            return True

        except Exception as e:
            raise Exception(f"Failed to delete user: {str(e)}")

    # Application Management Functions

    def get_application_id(self, app_name: str) -> str:
        """
        Get the application ID by name.

        Args:
            app_name: The application name (e.g., 'connecto')

        Returns:
            The application ID
        """
        try:
            response = self.client.table(TABLE_APPLICATIONS).select("id").eq("name", app_name).execute()

            if len(response.data) == 0:
                raise Exception(f"Application with name '{app_name}' not found")

            return response.data[0]["id"]

        except Exception as e:
            raise Exception(f"Failed to get application ID: {str(e)}")

    # Business Profile Functions

    def find_business_by_name_type(self, name: str, business_type: str) -> Optional[Dict[str, Any]]:
        """
        Find a business by name and type.

        Args:
            name: Business name
            business_type: Business type

        Returns:
            Business profile or None if not found
        """
        try:
            response = self.client.table(TABLE_RESTAURANTS) \
                .select("*") \
                .eq("name", name) \
                .eq("type", business_type) \
                .execute()

            if len(response.data) > 0:
                return response.data[0]
            return None

        except Exception as e:
            print(f"Error finding business: {str(e)}")
            return None

    def create_business(self, user_id: str, business_data: Dict[str, Any], app_name: str = APP_CONNECTO) -> Dict[str, Any]:
        """
        Create a new business profile and register it with the specified application.

        Args:
            user_id: The user's unique ID
            business_data: Dict containing business information
            app_name: The application name to register with (default: 'connecto')

        Returns:
            Dict containing the created business profile
        """
        try:
            # Ensure required fields are present
            required_fields = ["name", "type"]
            for field in required_fields:
                if field not in business_data:
                    raise ValueError(f"Missing required field: {field}")

            # Check if business already exists
            existing_business = self.find_business_by_name_type(
                business_data["name"],
                business_data["type"]
            )

            if existing_business:
                # Business already exists, register it with the application
                business_id = existing_business["id"]
                print(f"Business already exists with ID: {business_id}")

                # Check if already registered with this application
                app_id = self.get_application_id(app_name)
                self.register_business_with_app(business_id, app_id)

                return existing_business

            # Prepare business data
            data = {
                "user_id": user_id,
                "created_at": "now()",
                "updated_at": "now()",
                **business_data
            }

            # Create new business
            response = self.client.table(TABLE_RESTAURANTS).insert(data).execute()
            business = response.data[0]

            # Register with application
            app_id = self.get_application_id(app_name)
            self.register_business_with_app(business["id"], app_id)

            return business

        except Exception as e:
            raise Exception(f"Failed to create business profile: {str(e)}")

    def register_business_with_app(self, business_id: str, app_id: str, settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Register a business with an application.

        Args:
            business_id: The business's unique ID
            app_id: The application's unique ID
            settings: Application-specific settings (optional)

        Returns:
            Dict containing the registration information
        """
        try:
            # Check if already registered
            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("*") \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            if len(response.data) > 0:
                # Already registered, update settings if provided
                if settings:
                    return self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                        .update({"settings": settings, "is_active": True}) \
                        .eq("id", response.data[0]["id"]) \
                        .execute() \
                        .data[0]
                return response.data[0]

            # Register with application
            data = {
                "business_id": business_id,
                "application_id": app_id,
                "is_active": True,
                "settings": settings or {},
                "created_at": "now()",
                "updated_at": "now()"
            }

            response = self.client.table(TABLE_BUSINESS_APPLICATIONS).insert(data).execute()
            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to register business with application: {str(e)}")

    def get_business(self, business_id: str) -> Dict[str, Any]:
        """
        Get business profile by ID.

        Args:
            business_id: The business's unique ID

        Returns:
            Dict containing business information
        """
        try:
            response = self.client.table(TABLE_RESTAURANTS).select("*").eq("id", business_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Business with ID {business_id} not found")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to get business profile: {str(e)}")

    def get_business_with_app_settings(self, business_id: str, app_name: str = APP_CONNECTO) -> Dict[str, Any]:
        """
        Get business profile with application-specific settings.

        Args:
            business_id: The business's unique ID
            app_name: The application name (default: 'connecto')

        Returns:
            Dict containing business information with app settings
        """
        try:
            # Get business profile
            business = self.get_business(business_id)

            # Get application ID
            app_id = self.get_application_id(app_name)

            # Get application settings
            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("settings") \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            if len(response.data) > 0:
                business["app_settings"] = response.data[0]["settings"]
            else:
                business["app_settings"] = {}

            return business

        except Exception as e:
            raise Exception(f"Failed to get business with app settings: {str(e)}")

    def get_user_businesses(self, user_id: str, app_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all businesses owned by a user, optionally filtered by application.

        Args:
            user_id: The user's unique ID
            app_name: Filter by application name (optional)

        Returns:
            List of business profiles
        """
        try:
            if app_name:
                # Get application ID
                app_id = self.get_application_id(app_name)

                # Get businesses registered with this application
                query = f"""
                SELECT b.*
                FROM {TABLE_RESTAURANTS} b
                JOIN {TABLE_BUSINESS_APPLICATIONS} ba ON b.id = ba.business_id
                WHERE b.user_id = '{user_id}'
                AND ba.application_id = '{app_id}'
                AND ba.is_active = true
                """

                response = self.client.rpc("execute_sql", {"query": query}).execute()
                return response.data
            else:
                # Get all businesses for this user
                response = self.client.table(TABLE_RESTAURANTS).select("*").eq("user_id", user_id).execute()
                return response.data

        except Exception as e:
            raise Exception(f"Failed to get user businesses: {str(e)}")

    def update_business(self, business_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update business profile.

        Args:
            business_id: The business's unique ID
            data: Dict containing fields to update

        Returns:
            Dict containing updated business information
        """
        try:
            # Add updated_at timestamp
            update_data = {**data, "updated_at": "now()"}

            response = self.client.table(TABLE_RESTAURANTS).update(update_data).eq("id", business_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Business with ID {business_id} not found")

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to update business profile: {str(e)}")

    def update_business_app_settings(self, business_id: str, app_name: str, settings: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update application-specific settings for a business.

        Args:
            business_id: The business's unique ID
            app_name: The application name
            settings: Application-specific settings

        Returns:
            Dict containing updated settings
        """
        try:
            # Get application ID
            app_id = self.get_application_id(app_name)

            # Get business application record
            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("id, settings") \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            if len(response.data) == 0:
                # Not registered with this application yet
                return self.register_business_with_app(business_id, app_id, settings)

            # Update settings
            record_id = response.data[0]["id"]
            current_settings = response.data[0]["settings"]

            # Merge settings
            merged_settings = {**current_settings, **settings}

            response = self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .update({"settings": merged_settings, "updated_at": "now()"}) \
                .eq("id", record_id) \
                .execute()

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to update business app settings: {str(e)}")

    def delete_business(self, business_id: str) -> bool:
        """
        Delete a business profile.

        Args:
            business_id: The business's unique ID

        Returns:
            True if successful
        """
        try:
            self.client.table(TABLE_RESTAURANTS).delete().eq("id", business_id).execute()
            return True

        except Exception as e:
            raise Exception(f"Failed to delete business profile: {str(e)}")

    def deactivate_business_app(self, business_id: str, app_name: str) -> bool:
        """
        Deactivate a business for a specific application.

        Args:
            business_id: The business's unique ID
            app_name: The application name

        Returns:
            True if successful
        """
        try:
            # Get application ID
            app_id = self.get_application_id(app_name)

            # Update is_active flag
            self.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .update({"is_active": False, "updated_at": "now()"}) \
                .eq("business_id", business_id) \
                .eq("application_id", app_id) \
                .execute()

            return True

        except Exception as e:
            raise Exception(f"Failed to deactivate business app: {str(e)}")

    # Agent Configuration Functions

    def create_agent_config(self, business_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new agent configuration for a business.

        Args:
            business_id: The business's unique ID
            config_data: Dict containing agent configuration

        Returns:
            Dict containing the created agent configuration
        """
        try:
            # First, ensure the business is registered with Connecto
            app_id = self.get_application_id(APP_CONNECTO)
            self.register_business_with_app(business_id, app_id)

            # Check if config already exists
            existing = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()
            if len(existing.data) > 0:
                # Update existing config
                return self.update_agent_config(existing.data[0]["id"], config_data)

            # Prepare config data
            data = {
                "business_id": business_id,
                "created_at": "now()",
                "updated_at": "now()",
                "config": json.dumps(config_data)
            }

            response = self.client.table(TABLE_AGENT_CONFIGS).insert(data).execute()

            # Parse the JSON config in the response
            result = response.data[0]
            if isinstance(result["config"], str):
                result["config"] = json.loads(result["config"])

            return result

        except Exception as e:
            raise Exception(f"Failed to create agent configuration: {str(e)}")

    def get_agent_config(self, business_id: str) -> Dict[str, Any]:
        """
        Get agent configuration for a business.

        Args:
            business_id: The business's unique ID

        Returns:
            Dict containing agent configuration
        """
        try:
            response = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Agent configuration for business ID {business_id} not found")

            # Parse the JSON config
            config = response.data[0]
            if isinstance(config["config"], str):
                config["config"] = json.loads(config["config"])

            return config

        except Exception as e:
            raise Exception(f"Failed to get agent configuration: {str(e)}")

    def get_or_create_agent_config(self, business_id: str, default_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get agent configuration for a business or create it if it doesn't exist.

        Args:
            business_id: The business's unique ID
            default_config: Default configuration to use if none exists

        Returns:
            Dict containing agent configuration
        """
        try:
            response = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                # Create new config
                return self.create_agent_config(business_id, default_config)

            # Parse the JSON config
            config = response.data[0]
            if isinstance(config["config"], str):
                config["config"] = json.loads(config["config"])

            return config

        except Exception as e:
            raise Exception(f"Failed to get or create agent configuration: {str(e)}")

    def update_agent_config(self, config_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update agent configuration.

        Args:
            config_id: The configuration's unique ID
            config_data: Dict containing configuration to update

        Returns:
            Dict containing updated agent configuration
        """
        try:
            # Add updated_at timestamp
            update_data = {
                "updated_at": "now()",
                "config": json.dumps(config_data)
            }

            response = self.client.table(TABLE_AGENT_CONFIGS).update(update_data).eq("id", config_id).execute()

            if len(response.data) == 0:
                raise Exception(f"Agent configuration with ID {config_id} not found")

            # Parse the JSON config
            config = response.data[0]
            if isinstance(config["config"], str):
                config["config"] = json.loads(config["config"])

            return config

        except Exception as e:
            raise Exception(f"Failed to update agent configuration: {str(e)}")

    def update_agent_config_by_business(self, business_id: str, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update agent configuration by business ID.

        Args:
            business_id: The business's unique ID
            config_data: Dict containing configuration to update

        Returns:
            Dict containing updated agent configuration
        """
        try:
            # Get existing config
            response = self.client.table(TABLE_AGENT_CONFIGS).select("*").eq("business_id", business_id).execute()

            if len(response.data) == 0:
                # Create new config
                return self.create_agent_config(business_id, config_data)

            # Update existing config
            config_id = response.data[0]["id"]
            current_config = json.loads(response.data[0]["config"]) if isinstance(response.data[0]["config"], str) else response.data[0]["config"]

            # Merge configs
            merged_config = {**current_config, **config_data}

            return self.update_agent_config(config_id, merged_config)

        except Exception as e:
            raise Exception(f"Failed to update agent configuration: {str(e)}")

    # Call Logging Functions

    def log_call(self, business_id: str, call_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Log a call to the database.

        Args:
            business_id: The business's unique ID
            call_data: Dict containing call information

        Returns:
            Dict containing the created call log
        """
        try:
            # First, ensure the business is registered with Connecto
            app_id = self.get_application_id(APP_CONNECTO)
            self.register_business_with_app(business_id, app_id)

            # Prepare call data
            data = {
                "business_id": business_id,
                "timestamp": "now()",
                **call_data
            }

            response = self.client.table(TABLE_CALL_LOGS).insert(data).execute()

            return response.data[0]

        except Exception as e:
            raise Exception(f"Failed to log call: {str(e)}")

    def get_call_logs(self, business_id: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get call logs for a business.

        Args:
            business_id: The business's unique ID
            limit: Maximum number of logs to return
            offset: Number of logs to skip

        Returns:
            List of call logs
        """
        try:
            response = self.client.table(TABLE_CALL_LOGS) \
                .select("*") \
                .eq("business_id", business_id) \
                .order("timestamp", desc=True) \
                .range(offset, offset + limit - 1) \
                .execute()

            return response.data

        except Exception as e:
            raise Exception(f"Failed to get call logs: {str(e)}")

    def get_call_stats(self, business_id: str, days: int = 30) -> Dict[str, Any]:
        """
        Get call statistics for a business.

        Args:
            business_id: The business's unique ID
            days: Number of days to include in stats

        Returns:
            Dict containing call statistics
        """
        try:
            # Get total calls
            query_total = f"""
            SELECT COUNT(*) as total_calls,
                   AVG(duration) as avg_duration,
                   MAX(duration) as max_duration,
                   MIN(duration) as min_duration
            FROM connecto.{TABLE_CALL_LOGS}
            WHERE business_id = '{business_id}'
            AND timestamp > NOW() - INTERVAL '{days} days'
            """

            response_total = self.client.rpc("execute_sql", {"query": query_total}).execute()

            # Get calls by day
            query_by_day = f"""
            SELECT DATE_TRUNC('day', timestamp) as day,
                   COUNT(*) as calls
            FROM connecto.{TABLE_CALL_LOGS}
            WHERE business_id = '{business_id}'
            AND timestamp > NOW() - INTERVAL '{days} days'
            GROUP BY DATE_TRUNC('day', timestamp)
            ORDER BY day DESC
            """

            response_by_day = self.client.rpc("execute_sql", {"query": query_by_day}).execute()

            return {
                "total_calls": response_total.data[0]["total_calls"] if response_total.data else 0,
                "avg_duration": response_total.data[0]["avg_duration"] if response_total.data else 0,
                "max_duration": response_total.data[0]["max_duration"] if response_total.data else 0,
                "min_duration": response_total.data[0]["min_duration"] if response_total.data else 0,
                "calls_by_day": response_by_day.data if response_by_day.data else []
            }

        except Exception as e:
            raise Exception(f"Failed to get call statistics: {str(e)}")


# Create a singleton instance
_driver = None

def get_driver() -> SupabaseDriver:
    """
    Get the Supabase driver instance.

    Returns:
        SupabaseDriver instance
    """
    global _driver
    if _driver is None:
        _driver = SupabaseDriver()
    return _driver


# Utility functions for integration with agent.py

def get_business_by_name(business_name: str, app_name: str = APP_CONNECTO) -> Optional[Dict[str, Any]]:
    """
    Find a restaurant by name that is registered with a specific application.

    Args:
        business_name: Name of the restaurant
        app_name: Application name to filter by (default: 'connecto')

    Returns:
        Restaurant profile or None if not found
    """
    try:
        driver = get_driver()

        # First try to find by exact match
        response = driver.client.table(TABLE_RESTAURANTS).select("*").eq("name", business_name).execute()

        if len(response.data) > 0:
            restaurant = response.data[0]

            # Check if registered with the application
            app_id = driver.get_application_id(app_name)
            app_response = driver.client.table(TABLE_BUSINESS_APPLICATIONS) \
                .select("*") \
                .eq("business_id", restaurant["id"]) \
                .eq("application_id", app_id) \
                .eq("is_active", True) \
                .execute()

            if len(app_response.data) > 0:
                return restaurant

        # If not found or not registered, try a more flexible search
        query = f"""
        SELECT r.*
        FROM public.{TABLE_RESTAURANTS} r
        JOIN public.{TABLE_BUSINESS_APPLICATIONS} ba ON r.id = ba.business_id
        JOIN public.{TABLE_APPLICATIONS} a ON ba.application_id = a.id
        WHERE LOWER(r.name) LIKE LOWER('%{business_name}%')
        AND a.name = '{app_name}'
        AND ba.is_active = true
        LIMIT 1
        """

        response = driver.client.rpc("execute_sql", {"query": query}).execute()

        if response.data and len(response.data) > 0:
            return response.data[0]

        return None
    except Exception as e:
        print(f"Error finding restaurant: {str(e)}")
        return None


def load_business_config(business_id: str, app_name: str = APP_CONNECTO) -> Dict[str, Any]:
    """
    Load restaurant configuration for use with the agent.

    Args:
        business_id: The restaurant's unique ID
        app_name: Application name (default: 'connecto')

    Returns:
        Dict containing restaurant and agent configuration
    """
    driver = get_driver()

    # Get restaurant profile with app settings
    restaurant = driver.get_business_with_app_settings(business_id, app_name)

    # Get agent configuration
    try:
        agent_config = driver.get_or_create_agent_config(business_id, {
            "voice": "coral",
            "temperature": 0.8,
            "demo_mode": False
        })
        config = agent_config["config"]
    except Exception as e:
        print(f"Error getting agent config: {str(e)}")
        # Use default configuration if none exists
        config = {
            "voice": "coral",
            "temperature": 0.8,
            "demo_mode": False
        }

    # Combine restaurant profile and agent configuration
    return {
        "business_id": business_id,
        "business_name": restaurant["name"],
        "business_phone": restaurant.get("contact_phone"),
        "business_email": restaurant.get("contact_email"),
        "business_address": restaurant.get("address"),
        "business_hours": restaurant.get("opening_hours"),
        "app_settings": restaurant.get("app_settings", {}),
        "agent_config": config
    }


def configure_agent_from_db(business_id: Optional[str] = None,
                           business_name: Optional[str] = None,
                           app_name: str = APP_CONNECTO,
                           user_id: Optional[str] = None,
                           create_if_missing: bool = False,
                           business_type: Optional[str] = None) -> Dict[str, Any]:
    """
    Configure the agent using database settings.
    This function can be called from agent.py to load business-specific settings.

    Args:
        business_id: The restaurant's unique ID (optional)
        business_name: Name of the restaurant (optional)
        app_name: Application name (default: 'connecto')
        user_id: User ID for creating a new business if needed (optional)
        create_if_missing: Whether to create a new business if not found (default: False)
        business_type: Deprecated, kept for backward compatibility

    Returns:
        Dict containing restaurant and agent configuration
    """
    driver = get_driver()

    # If business_id is provided, use it directly
    if business_id:
        # Check if restaurant exists and is registered with the application
        try:
            return load_business_config(business_id, app_name)
        except Exception as e:
            print(f"Error loading restaurant config: {str(e)}")
            if not create_if_missing:
                raise

    # If business_name is provided, look up the restaurant
    if business_name:
        restaurant = get_business_by_name(business_name, app_name)
        if restaurant:
            return load_business_config(restaurant["id"], app_name)

        # Restaurant not found, create it if requested
        if create_if_missing and user_id:
            try:
                # Create new restaurant entry
                restaurant_data = {
                    "name": business_name,
                    "contact_email": "<EMAIL>",  # Required field
                    "address": "Demo Address",  # Required field
                    "user_id": user_id,
                    "is_active": True
                }

                # Insert directly into restaurants table
                response = driver.client.table(TABLE_RESTAURANTS).insert(restaurant_data).execute()
                restaurant = response.data[0]

                # Register with Connecto
                register_business_for_connecto(restaurant["id"])

                return load_business_config(restaurant["id"], app_name)
            except Exception as e:
                print(f"Error creating restaurant: {str(e)}")

    # Return default configuration if no restaurant found
    return {
        "business_id": None,
        "business_name": business_name or "Demo Restaurant",
        "business_phone": None,
        "business_email": None,
        "business_address": None,
        "app_settings": {},
        "agent_config": {
            "voice": "coral",
            "temperature": 0.8,
            "demo_mode": False
        }
    }


def log_agent_call(business_id: str,
                  caller_number: Optional[str] = None,
                  duration: Optional[int] = None,
                  transcript: Optional[str] = None,
                  summary: Optional[str] = None,
                  call_id: Optional[str] = None,
                  direction: Optional[str] = None,
                  status: Optional[str] = None,
                  room_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Log a call handled by the agent.

    Args:
        business_id: The business's unique ID
        caller_number: Phone number of the caller (optional)
        duration: Call duration in seconds (optional)
        transcript: Call transcript (optional)
        summary: Call summary (optional)
        call_id: Unique identifier for the call (optional)
        direction: Call direction ('inbound' or 'outbound') (optional)
        status: Call status (e.g., 'initiated', 'completed', 'failed') (optional)
        room_name: LiveKit room name for the call (optional)

    Returns:
        Dict containing the created call log
    """
    driver = get_driver()

    call_data = {
        "caller_number": caller_number,
        "duration": duration,
        "transcript": transcript,
        "summary": summary,
        "call_id": call_id,
        "direction": direction,
        "status": status,
        "room_name": room_name
    }

    # Filter out None values
    call_data = {k: v for k, v in call_data.items() if v is not None}

    return driver.log_call(business_id, call_data)


def register_business_for_connecto(business_id: str, settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Register an existing business for Connecto services.

    Args:
        business_id: The business's unique ID
        settings: Connecto-specific settings (optional)

    Returns:
        Dict containing the registration information
    """
    driver = get_driver()
    app_id = driver.get_application_id(APP_CONNECTO)
    return driver.register_business_with_app(business_id, app_id, settings)


def get_business_stats(business_id: str, days: int = 30) -> Dict[str, Any]:
    """
    Get comprehensive statistics for a business.

    Args:
        business_id: The business's unique ID
        days: Number of days to include in stats (default: 30)

    Returns:
        Dict containing business statistics
    """
    driver = get_driver()

    # Get business profile
    business = driver.get_business_with_app_settings(business_id, APP_CONNECTO)

    # Get call statistics
    call_stats = driver.get_call_stats(business_id, days)

    # Get recent calls
    recent_calls = driver.get_call_logs(business_id, limit=5)

    return {
        "business": business,
        "call_stats": call_stats,
        "recent_calls": recent_calls
    }