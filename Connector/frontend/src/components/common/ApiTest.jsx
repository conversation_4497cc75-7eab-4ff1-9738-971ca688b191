import React, { useState } from 'react';
import { healthAPI } from '../../utils/api';
import Button from './Button';
import { Card, CardHeader, CardTitle, CardContent } from './Card';

const ApiTest = () => {
  const [status, setStatus] = useState('Not tested');
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    try {
      const response = await healthAPI.check();
      console.log('Health check response:', response);
      setStatus(`✅ Connected - ${response.data.message}`);
    } catch (error) {
      console.error('Health check error:', error);
      setStatus(`❌ Failed - ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>API Connection Test</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <Button onClick={testConnection} loading={loading}>
            Test API Connection
          </Button>
          <span className="text-sm">{status}</span>
        </div>
        <p className="text-xs text-secondary-500 mt-2">
          API URL: {import.meta.env.VITE_API_URL}
        </p>
      </CardContent>
    </Card>
  );
};

export default ApiTest;
