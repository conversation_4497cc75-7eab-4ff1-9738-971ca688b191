import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Building2, 
  Phone, 
  Play, 
  Settings,
  HelpCircle 
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Businesses', href: '/businesses', icon: Building2 },
  { name: 'Call Logs', href: '/call-logs', icon: Phone },
  { name: 'Voice Preview', href: '/preview', icon: Play },
];

const secondaryNavigation = [
  { name: 'Settings', href: '/settings', icon: Settings },
  { name: 'Help', href: '/help', icon: HelpCircle },
];

const Sidebar = () => {
  const location = useLocation();

  const isActive = (href) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  const NavItem = ({ item }) => (
    <Link
      to={item.href}
      className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
        isActive(item.href)
          ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
          : 'text-secondary-600 hover:bg-secondary-50 hover:text-secondary-900'
      }`}
    >
      <item.icon
        className={`mr-3 flex-shrink-0 h-5 w-5 ${
          isActive(item.href) ? 'text-primary-600' : 'text-secondary-400 group-hover:text-secondary-500'
        }`}
      />
      {item.name}
    </Link>
  );

  return (
    <div className="flex flex-col w-64 bg-white border-r border-secondary-200 h-full">
      {/* Main Navigation */}
      <nav className="flex-1 px-4 py-6 space-y-1">
        <div className="space-y-1">
          {navigation.map((item) => (
            <NavItem key={item.name} item={item} />
          ))}
        </div>
        
        {/* Divider */}
        <div className="border-t border-secondary-200 my-6"></div>
        
        {/* Secondary Navigation */}
        <div className="space-y-1">
          {secondaryNavigation.map((item) => (
            <NavItem key={item.name} item={item} />
          ))}
        </div>
      </nav>

      {/* Footer */}
      <div className="px-4 py-4 border-t border-secondary-200">
        <div className="text-xs text-secondary-500 text-center">
          <p>Connecto AI</p>
          <p>by SME Analytica</p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
