import React from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';

const BusinessDetail = () => {
  const { id } = useParams();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-secondary-900">Business Details</h1>
        <p className="text-secondary-600">
          Business ID: {id}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Business Information</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-secondary-600">
            This page will contain detailed business information and configuration options.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default BusinessDetail;
