import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  Building2, 
  Phone, 
  TrendingUp, 
  Users, 
  Plus,
  ArrowRight,
  Activity
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from '../components/common/Card';
import Button from '../components/common/Button';
import LoadingSpinner from '../components/common/LoadingSpinner';
import { businessAPI, callLogsAPI } from '../utils/api';
import { formatDate, formatDuration } from '../utils/helpers';

const Dashboard = () => {
  const [businesses, setBusinesses] = useState([]);
  const [stats, setStats] = useState({
    totalBusinesses: 0,
    totalCalls: 0,
    avgCallDuration: 0,
    activeAgents: 0,
  });
  const [recentCalls, setRecentCalls] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch businesses
      const businessResponse = await businessAPI.getUserBusinesses();
      const businessData = businessResponse.data.data || [];
      setBusinesses(businessData);

      // Calculate stats
      const totalBusinesses = businessData.length;
      let totalCalls = 0;
      let totalDuration = 0;
      const recentCallsData = [];

      // Fetch call stats for each business
      for (const business of businessData.slice(0, 3)) { // Limit to first 3 for performance
        try {
          const callStatsResponse = await callLogsAPI.getStats(business.id, 30);
          const callStats = callStatsResponse.data.data;
          totalCalls += callStats.total_calls || 0;
          totalDuration += callStats.total_duration || 0;

          // Fetch recent calls
          const callLogsResponse = await callLogsAPI.getLogs(business.id, 5);
          const callLogs = callLogsResponse.data.data || [];
          recentCallsData.push(...callLogs.map(call => ({
            ...call,
            business_name: business.name,
          })));
        } catch (error) {
          console.error(`Error fetching data for business ${business.id}:`, error);
        }
      }

      setStats({
        totalBusinesses,
        totalCalls,
        avgCallDuration: totalCalls > 0 ? Math.round(totalDuration / totalCalls) : 0,
        activeAgents: businessData.filter(b => b.is_active).length,
      });

      // Sort recent calls by timestamp and take the most recent
      setRecentCalls(
        recentCallsData
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(0, 5)
      );

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Dashboard</h1>
          <p className="text-secondary-600">
            Welcome back! Here's what's happening with your AI voice receptionists.
          </p>
        </div>
        <Button
          as={Link}
          to="/businesses/new"
          icon={Plus}
        >
          Add Business
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-primary-100 rounded-lg">
                <Building2 className="w-6 h-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Businesses</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.totalBusinesses}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-success-100 rounded-lg">
                <Phone className="w-6 h-6 text-success-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Total Calls</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.totalCalls}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-warning-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-warning-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Avg Call Duration</p>
                <p className="text-2xl font-bold text-secondary-900">
                  {formatDuration(stats.avgCallDuration)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Activity className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-secondary-600">Active Agents</p>
                <p className="text-2xl font-bold text-secondary-900">{stats.activeAgents}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Businesses */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Your Businesses</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                as={Link}
                to="/businesses"
                icon={ArrowRight}
                iconPosition="right"
              >
                View all
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {businesses.length === 0 ? (
              <div className="text-center py-8">
                <Building2 className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                <p className="text-secondary-600 mb-4">No businesses yet</p>
                <Button
                  as={Link}
                  to="/businesses/new"
                  size="sm"
                  icon={Plus}
                >
                  Add your first business
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {businesses.slice(0, 3).map((business) => (
                  <div
                    key={business.id}
                    className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg hover:bg-secondary-50 transition-colors"
                  >
                    <div>
                      <h4 className="font-medium text-secondary-900">{business.name}</h4>
                      <p className="text-sm text-secondary-600">{business.business_type}</p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      as={Link}
                      to={`/businesses/${business.id}`}
                    >
                      View
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Calls */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Calls</CardTitle>
              <Button
                variant="ghost"
                size="sm"
                as={Link}
                to="/call-logs"
                icon={ArrowRight}
                iconPosition="right"
              >
                View all
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {recentCalls.length === 0 ? (
              <div className="text-center py-8">
                <Phone className="w-12 h-12 text-secondary-400 mx-auto mb-4" />
                <p className="text-secondary-600">No calls yet</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentCalls.map((call) => (
                  <div
                    key={call.id}
                    className="flex items-center justify-between p-3 border border-secondary-200 rounded-lg"
                  >
                    <div>
                      <h4 className="font-medium text-secondary-900">
                        {call.caller_number || 'Unknown'}
                      </h4>
                      <p className="text-sm text-secondary-600">{call.business_name}</p>
                      <p className="text-xs text-secondary-500">
                        {formatDate(call.timestamp, 'MMM dd, h:mm a')}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-secondary-900">
                        {formatDuration(call.duration)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
