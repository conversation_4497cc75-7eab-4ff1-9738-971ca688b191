import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('access_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  register: (userData) => api.post('/users/register', userData),
  login: (credentials) => api.post('/users/login', credentials),
  getCurrentUser: () => api.get('/users/me'),
};

// Business API
export const businessAPI = {
  create: (businessData) => api.post('/businesses', businessData),
  getById: (businessId) => api.get(`/businesses/${businessId}`),
  update: (businessId, businessData) => api.put(`/businesses/${businessId}`, businessData),
  getUserBusinesses: () => api.get('/businesses/user'),
};

// Agent Configuration API
export const agentAPI = {
  getConfig: (businessId) => api.get(`/agent-configs/${businessId}`),
  updateConfig: (businessId, config) => api.post(`/agent-configs/${businessId}`, { config }),
};

// Call Logs API
export const callLogsAPI = {
  getLogs: (businessId, limit = 10) => api.get(`/call-logs/${businessId}?limit=${limit}`),
  getStats: (businessId, days = 30) => api.get(`/call-logs/${businessId}/stats?days=${days}`),
  create: (callData) => api.post('/call-logs', callData),
};

// Voice Preview API
export const previewAPI = {
  generate: (previewData) => api.post('/preview', previewData),
};

// Health Check API
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
