// Business types supported by Connecto
export const BUSINESS_TYPES = [
  { value: 'auto_service', label: 'Auto Service Center' },
  { value: 'nail_salon', label: 'Nail Salon' },
  { value: 'restaurant', label: 'Restaurant' },
  { value: 'barbershop', label: 'Barbershop' },
  { value: 'medical_office', label: 'Medical Office' },
  { value: 'dental_office', label: 'Dental Office' },
  { value: 'spa', label: 'Spa' },
  { value: 'law_firm', label: 'Law Firm' },
  { value: 'real_estate', label: 'Real Estate Office' },
  { value: 'fitness_center', label: 'Fitness Center' },
];

// Voice options for TTS
export const VOICE_OPTIONS = [
  { value: 'alloy', label: 'Alloy (Neutral)' },
  { value: 'echo', label: 'Echo (Male)' },
  { value: 'fable', label: 'Fable (British Male)' },
  { value: 'onyx', label: 'Onyx (Deep Male)' },
  { value: 'nova', label: '<PERSON> (Female)' },
  { value: 'shimmer', label: '<PERSON><PERSON> (Female)' },
];

// Default business hours
export const DEFAULT_BUSINESS_HOURS = {
  monday: { open: '09:00', close: '17:00', closed: false },
  tuesday: { open: '09:00', close: '17:00', closed: false },
  wednesday: { open: '09:00', close: '17:00', closed: false },
  thursday: { open: '09:00', close: '17:00', closed: false },
  friday: { open: '09:00', close: '17:00', closed: false },
  saturday: { open: '10:00', close: '16:00', closed: false },
  sunday: { open: '10:00', close: '16:00', closed: true },
};

// Navigation items
export const NAV_ITEMS = [
  { path: '/dashboard', label: 'Dashboard', icon: 'LayoutDashboard' },
  { path: '/businesses', label: 'Businesses', icon: 'Building2' },
  { path: '/call-logs', label: 'Call Logs', icon: 'Phone' },
  { path: '/preview', label: 'Voice Preview', icon: 'Play' },
];

// API response status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
};

// Local storage keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  USER: 'user',
  THEME: 'theme',
};

// App configuration
export const APP_CONFIG = {
  name: import.meta.env.VITE_APP_NAME || 'Connecto AI Voice Receptionist',
  version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:5000',
};
